<div class="calendar-header pr-2">
    @for (day of ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']; track $index)
    {
       <div class="header-day">{{ day }}</div>
    }
</div>
<div class="calendar-body overflow-hidden">
   @for (week of monthWeeks; track $index){
      <div class="week">
         @for (day of week.days; track $index){
           <div class="day" [class.not-in-month]="!day.inMonth">
             <div class="date-header flex gap-2 mb-1">{{ day.date | date:'dd' }}
                @let rdesc = getDescription(day.date);
                @if (rdesc && rdesc.length){
                    <span [ngClass]="{'description-style': true}"
                        class="justify-center text-center items-center pb-2  mb-1">
                        {{ rdesc }}
                    </span>
                }
             </div>
             <div class="resource-availability mt-1">
                @for (resource of objsession; track $index){
                    <div>
                    @let rsess = getResourceSessions(resource, day.date);
                    @for (sess of rsess; track $index){
                        <div>
                            <div class="pl-2 pr-2">
                                <div [style.border-top]="'4px solid ' + (sess.preferredtimeavl > 0 ? '#74c2db' : 'lightred')"
                                    [ngClass]="getButtonClass(sess)"
                                    (click)="onSlotSelected(sess)"
                                    matTooltip="{{ 'Start Time: ' + (sess.starttime | slice:0:5) + ', End Time: ' + (sess.endtime | slice:0:5) + ', Slots Available: ' + sess.preferredtimeavl }}"
                                    class="session-button mb-1">
                                    <div class="flex flex-col items-center ">
                                        <span class="text-center font-medium justify-center">
                                            <span class="text-base"> {{ sess.sessiondesc | slice:0:3 }}
                                            </span> - <span class="text-sm">{{ sess.availablepercentage
                                                }}%</span>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                    </div>
                }
            </div>
           </div>
         }
      </div>
   }
</div>
