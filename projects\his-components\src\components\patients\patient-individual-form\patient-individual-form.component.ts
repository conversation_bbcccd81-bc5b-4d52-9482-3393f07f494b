import { CommonModule, DatePipe, DecimalPipe } from '@angular/common';
import { Component, Inject, Input, OnInit } from '@angular/core';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { EcmedWebapiModelsPatientmgmtEditIndividualModel, PatientService } from 'ecmed-api/visitmgmt';
import { VisitData } from '../visit.model';
import { CommonService } from '../common.service';
import { AutoFormatDateDirective } from '@his-components/services';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatError, MatFormField, MatLabel } from '@angular/material/form-field';
import {MatDatepickerModule} from '@angular/material/datepicker';
import {MatTabsModule} from '@angular/material/tabs';
import {MatCheckboxModule} from '@angular/material/checkbox';
import {NgxMaterialIntlTelInputComponent} from 'ngx-material-intl-tel-input'
import {MaterialPackage} from '@his-components/utils';
import { HisCommonAddressComponent } from '../his-common-address/his-common-address.component';
@Component({
    selector: 'his-pat-indv-form',
    standalone: true,
    imports: [NgxMaterialIntlTelInputComponent, CommonModule, MaterialPackage, FormsModule, ReactiveFormsModule, AutoFormatDateDirective, HisCommonAddressComponent],
    templateUrl: './patient-individual-form.component.html',
    styleUrl: './patient-individual-form.component.scss',
    providers: [DatePipe, DecimalPipe,
    ],
})
export class PatientIndividualFormComponent implements OnInit {

    patientDetails: EcmedWebapiModelsPatientmgmtEditIndividualModel = {};
    private _inputData: any;
    patientID: any;
    patientname: any;
    today = new Date();
    isClicked: boolean = false;
    patientDetail: any;
    smsalert: any;
    organDonor?: string;
    speakEnglish?: string;
    anotherVariable: any;
    identifierNo: any;
    separateDialCode = false;
    selectedPatient?: EcmedWebapiModelsPatientmgmtEditIndividualModel;
    visitData?: VisitData;
    minDate: Date;
    maxDate: Date;
    address: any = {};
    constructor(
        public _dialog: MatDialog,
        public _API: PatientService,
        public commonServices: CommonService,
        @Inject(MAT_DIALOG_DATA) public data: any,
        private dialogRef: MatDialogRef<PatientIndividualFormComponent>
    ) {
        const currentYear = new Date().getFullYear();
        this.minDate = new Date(currentYear - 20, 0, 1);
        this.maxDate = new Date(currentYear + 1, 11, 31);
        console.log(data, "DIALOGDATA");
        this.fetchPatientDetails(data);
    }

    myForm: FormGroup = new FormGroup({
        phoneNumber: new FormControl(''),
        phoneNumber2: new FormControl(''),
        phoneNumber3: new FormControl(''),
        phoneNumber4: new FormControl(''),
    });

    ngOnInit(): void {
        console.log(this.dialogRef)
        console.log(this.data)
        const codeTypes = ['PT', 'SX', 'RE', 'RC', 'BG', 'NA', 'MS', 'TC', 'ED', 'ON', 'ID', 'LG', 'RT', 'AD', 'CY'];
        this.commonServices.fetchVisitData(codeTypes).subscribe(
            (response: any) => {
                this.visitData = response;
            }
        );
        if (this.data?.IDENTIFIER) {
            console.log("travelling")
            this.fetchPatientDetails(this.data.IDENTIFIER?.toString())
        }
    }
    onCheckboxOrganDonor(event: any) {
        this.patientDetails.OrganDonor = event.checked ? 'Y' : 'N';
    }
    onCheckboxSpeakEnglish(event: any) {
        this.patientDetails.SpeakEnglish = event.checked ? 'Y' : 'N';
    }
    onCheckboxAlertEmail(event: any) {
        this.patientDetails.AlertEmail = event.checked ? 'Y' : 'N';
    }
    onCheckboxAlertSMS(event: any) {
        this.patientDetails.AlertSMS = event.checked ? 'Y' : 'N';
    }
    public handleAllocatePhone(value:any[]) {
        if (value?.length) {
            console.log("handleAllocatePhone", value[0]);

            // Extract phone numbers from individual data
            const tel = value[0].TEL || '';
            const tel2 = value[0].TEL2 || '';
            const tel3 = value[0].TEL3 || '';
            const tel4 = value[0].TEL4 || '';

            console.log('Binding individual phone numbers:', { tel, tel2, tel3, tel4 });

            // Update form with phone numbers
            this.myForm.patchValue({
                phoneNumber: tel,
                phoneNumber2: tel2,
                phoneNumber3: tel3,
                phoneNumber4: tel4
            });

            // Force change detection
            this.myForm.markAsDirty();
            this.myForm.updateValueAndValidity();
        }
    }

    fetchPatientDetails(identifier: string): void {
        this._API.patientGetIndividualDetailGet({id:identifier}).subscribe(
            (data: object[]) => {
                this.handleAllocatePhone(data)
                console.log("dataInd", data)
                console.log(data, "PatientForm")
                const patientDetails = <any>( Array.isArray(data) ? data[0] : data);
                if (patientDetails) {
                    this.patientDetails.Identifier = patientDetails["IDENTIFIER"].toString();
                    this.patientDetails.Name = patientDetails["NAME"];
                    this.patientDetails.IDNo = patientDetails["IDNO"];
                    this.patientDetails.IDType = patientDetails["IDTYPE"];
                    this.patientDetails.Address1 = patientDetails["ADDRESS1"];
                    this.patientDetails.Address2 = patientDetails["ADDRESS2"];
                    this.patientDetails.Address3 = patientDetails["ADDRESS3"];
                    this.patientDetails.Address4 = patientDetails["ADDRESS4"];
                    this.patientDetails.AddressType = patientDetails["ADDRESSTYPE"];
                    this.patientDetails.AlertEmail = patientDetails["ALERTEMAIL"];
                    this.patientDetails.AlertSMS = patientDetails["ALERTSMS"];
                    this.patientDetails.BloodGroup = patientDetails["BLOODGROUP"];
                    this.patientDetails.City = patientDetails["CITY"].toString();
                    this.patientDetails.Country = patientDetails["COUNTRY"];
                    this.patientDetails.DateOfBirth = patientDetails["DOB"];
                    this.patientDetails.DateOfBirth = patientDetails["DOB"];
                    this.patientDetails.Education = patientDetails["EDUCATION"];
                    this.patientDetails.Email = patientDetails["EMAIL"];
                    this.patientDetails.Ethnicity = patientDetails["ETHNICITY"];
                    this.patientDetails.Gender = patientDetails["GENDER"];
                    this.patientDetails.LanguageCode = patientDetails["LANGUAGECODE"];
                    this.patientDetails.MaritalStatus = patientDetails["MARITALSTATUS"];
                    this.patientDetails.Merged = patientDetails["MERGED"];
                    this.patientDetails.Title = patientDetails["TITLE"];
                    this.patientDetails.Nationality = patientDetails["NATIONALITY"];
                    this.patientDetails.Occupation = patientDetails["OCCUPATION"];
                    this.patientDetails.OrganDonor = patientDetails["ORGANDONOR"];
                    this.patientDetails.PatientId = patientDetails["PATIENTID"];
                    this.patientDetails.PlaceOfBirth = patientDetails["PLACE_OF_BIRTH"];
                    this.patientDetails.PostalCode = patientDetails["POSTALCODE"];
                    this.patientDetails.Religion = patientDetails["RELIGION"];
                    this.patientDetails.ResidenceCode = patientDetails["RESIDENCE_CODE"];
                    this.patientDetails.SpeakEnglish = patientDetails["SPEAKENGLISH"];
                    this.patientDetails.State = patientDetails["STATE"].toString();
                    this.patientDetails.Telephone2 = patientDetails["TEL2"];
                    this.patientDetails.Telephone3 = patientDetails["TEL3"];
                    this.patientDetails.Telephone4 = patientDetails["TEL4"];
                    this.patientDetails.Telephone = patientDetails["TEL"];
                    this.patientDetails.VIPStatus = patientDetails["VIPSTATUS"];
                    this.patientDetails._OrgCode = patientDetails["_ORGCODE"];
                }
            }
        );
    }

    restrictToNumbersAndPlus(event: KeyboardEvent) {
        const allowedKeys = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '+', 'Backspace', 'Tab', 'ArrowLeft', 'ArrowRight', 'Delete', 'Enter'];
        if (!allowedKeys.includes(event.key)) {
            event.preventDefault();
        }
        if (event.key === '+' && event.currentTarget && 
             (<any>event.currentTarget)["selectionStart"] !== 0) {
            event.preventDefault();
        }
    }

    calculateAgeFromDOB(dob?: string|null): number|null {
        if (dob) return this.calculateAge(dob);
        return null;
    }

    public calculateAge(dob: string): number {
        const today = new Date();
        const birthDate = new Date(dob);
        let age = today.getFullYear() - birthDate.getFullYear();
        const m = today.getMonth() - birthDate.getMonth();
        if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
            age--;
        }
        return age;
    }

    goback(): void {
        this.dialogRef.close();
    }

    onSubmit() {
        if (this.data) {
            this.Updateindividuals();
        } else {
            this.saveindiviregistrationList();
        }
    }

    public saveindiviregistrationList(): void {
        this.patientDetails.Telephone = this.myForm?.value?.phoneNumber || ""
        this.patientDetails.Telephone2 = this.myForm?.value?.phoneNumber2 || ""
        this.patientDetails.Telephone3 = this.myForm?.value?.phoneNumber3 || ""
        this.patientDetails.Telephone4 = this.myForm?.value?.phoneNumber4 || ""
        if (
            !this.patientDetails.IDNo ||
            !this.patientDetails.DateOfBirth ||
            !this.patientDetails.Name ||
            !this.patientDetails.Telephone
        ) {
            return;
        }
        let objSave1 = {
            ...this.patientDetails,
            Identifier: '',
            _OrgCode: 0,
            Merged: "N",
            PatientId: "0",
            // HRN: "",
            Telephone: this.patientDetails.Telephone || '',
            Telephone2: this.patientDetails.Telephone2 || '',
            Telephone3: this.patientDetails.Telephone3 || '',
            Telephone4: this.patientDetails.Telephone4 || '',
        }
        console.log(objSave1, 'save');
        const requestParams = {
            ecmedWebapiModelsPatientmgmtEditIndividualModel: objSave1
        };
        this._API.patientCreateNewIndividualPost(requestParams).subscribe((apiResult: any) => {
            if (apiResult) {
                this.dialogRef.close(this.patientDetails);
            }
        });
    }

    public Updateindividuals(): void {
        this.patientDetails.Telephone = this.myForm?.value?.phoneNumber || ""
        this.patientDetails.Telephone2 = this.myForm?.value?.phoneNumber2 || ""
        this.patientDetails.Telephone3 = this.myForm?.value?.phoneNumber3 || ""
        this.patientDetails.Telephone4 = this.myForm?.value?.phoneNumber4 || ""
        if (
            !this.patientDetails.IDNo ||
            !this.patientDetails.DateOfBirth ||
            !this.patientDetails.Name ||
            !this.patientDetails.Telephone
        ) {
            return;
        }
        let objSave1 = {
            ...this.patientDetails,
            Identifier: this.patientDetails.Identifier,
            _OrgCode: 0,
            Merged: "N",
            PatientId: this.patientDetails.Identifier,
            HRN: '',
            AlertEmail: "Y",
            AlertSMS: "Y",
            OrganDonor: "Y",
            SpeakEnglish: "Y",
            Telephone: this.patientDetails.Telephone || '',
            Telephone2: this.patientDetails.Telephone2 || '',
            Telephone3: this.patientDetails.Telephone3 || '',
            Telephone4: this.patientDetails.Telephone4 || '',
        }
        let objSave: any = JSON.stringify(objSave1, null, 2);
        this._API.patientUpdateRegIndividualPost(objSave).subscribe((apiResult: any) => {
            if (apiResult) {
                this.dialogRef.close(this.patientDetails);

            }
        });
    }
    allCountries = [
        "AF", "AX", "AL", "DZ", "AS", "AD", "AO", "AI", "AQ", "AG", "AR", "AM", "AW", "AC", "AU", "AT", "AZ", "BS", "BH",
        "BD", "BB", "BY", "BE", "BZ", "BJ", "BM", "BT", "BO", "BA", "BW", "BR", "IO", "BN", "BG", "BF", "BI", "KH", "CM",
        "CA", "CV", "KY", "CF", "TD", "CL", "CN", "CX", "CC", "CO", "KM", "CG", "CK", "CR", "CI", "HR", "CU", "CY", "CZ",
        "CD", "DK", "DJ", "DM", "DO", "EC", "EG", "SV", "GQ", "ER", "EE", "SZ", "ET", "FK", "FO", "FJ", "FI", "FR", "GF",
        "PF", "GA", "GM", "GE", "DE", "GH", "GI", "GR", "GL", "GD", "GP", "GU", "GT", "GG", "GN", "GW", "GY", "HT", "VA",
        "HN", "HK", "HU", "IS", "IN", "ID", "IR", "IQ", "IE", "IM", "IL", "IT", "JM", "JP", "JE", "JO", "KZ", "KE", "KI",
        "KP", "KR", "XK", "KW", "KG", "LA", "LV", "LB", "LS", "LR", "LY", "LI", "LT", "LU", "MO", "MG", "MW", "MY", "MV",
        "ML", "MT", "MH", "MQ", "MR", "MU", "YT", "MX", "FM", "MD", "MC", "MN", "ME", "MS", "MA", "MZ", "MM", "NA", "NR",
        "NP", "NL", "NC", "NZ", "NI", "NE", "NG", "NU", "NF", "MK", "MP", "NO", "OM", "PK", "PW", "PS", "PA", "PG", "PY",
        "PE", "PH", "PN", "PL", "PT", "PR", "QA", "RE", "RO", "RU", "RW", "BL", "SH", "KN", "LC", "MF", "PM", "VC", "WS",
        "SM", "ST", "SA", "SN", "RS", "SC", "SL", "SG", "SX", "SK", "SI", "SB", "SO", "ZA", "GS", "SS", "ES", "LK", "SD",
        "SR", "SJ", "SE", "CH", "SY", "TW", "TJ", "TZ", "TH", "TL", "TG", "TK", "TO", "TT", "TN", "TR", "TM", "TC", "TV",
        "UG", "UA", "AE", "GB", "US", "UY", "UZ", "VU", "VE", "VN", "VG", "VI", "WF", "YE", "ZM", "ZW"
    ];

    // Methods for his-common-address component integration
    public getAddressData(): any {
        return {
            ADDRESSTYPE: this.address.AddressType || this.patientDetails.AddressType,
            ADDRESS1: this.address.Address1 || this.patientDetails.Address1,
            ADDRESS2: this.address.Address2 || this.patientDetails.Address2,
            ADDRESS3: this.address.Address3 || this.patientDetails.Address3,
            ADDRESS4: this.address.Address4 || this.patientDetails.Address4,
            COUNTRY: this.address.Country || this.patientDetails.Country,
            STATE: this.address.State || this.patientDetails.State,
            CITY: this.address.City || this.patientDetails.City,
            POSTALCODE: this.address.PostalCode || this.patientDetails.PostalCode
        };
    }

    public onAddressChange(addressData: any): void {
        // Update the address object
        this.address = {
            AddressType: addressData.ADDRESSTYPE,
            Address1: addressData.ADDRESS1,
            Address2: addressData.ADDRESS2,
            Address3: addressData.ADDRESS3,
            Address4: addressData.ADDRESS4,
            Country: addressData.COUNTRY,
            State: addressData.STATE,
            City: addressData.CITY,
            PostalCode: addressData.POSTALCODE
        };

        // Update the patient details
        this.patientDetails.AddressType = addressData.ADDRESSTYPE;
        this.patientDetails.Address1 = addressData.ADDRESS1;
        this.patientDetails.Address2 = addressData.ADDRESS2;
        this.patientDetails.Address3 = addressData.ADDRESS3;
        this.patientDetails.Address4 = addressData.ADDRESS4;
        this.patientDetails.Country = addressData.COUNTRY;
        this.patientDetails.State = addressData.STATE;
        this.patientDetails.City = addressData.CITY;
        this.patientDetails.PostalCode = addressData.POSTALCODE;
    }
}
