import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { MaterialPackage } from '@app-appt/utils';
import { dateUtils } from 'ec-ngcore';

@Component({
    selector: 'his-month-view',
    standalone: true,
    imports: [
        CommonModule,
        MaterialPackage
    ],
    templateUrl: './month-view.component.html',
    styleUrls: ['./month-view.component.scss'],
    providers: [DatePipe]
})
export class MonthViewComponent {
    @Input() monthWeeks: any[] = [];
    @Input() objsession?: any[];
    @Input() headervalues: any;
    @Input() objLoad: any = {};
    @Output() slotSelected = new EventEmitter<any>();

    constructor(public datePipe: DatePipe) {}

    onSlotSelected(slot: any): void {
        this.slotSelected.emit(slot);
    }

    getDescription(date: Date): string {
        const entry = this.headervalues?.find((d: any) => d.dateheader === date);
        return entry ? entry.description : '';
    }

    getResourceSessions(resource: any, dt: Date): any[] {
        if (!resource || !resource.dates || !dt) return [];
        const dateKey = dateUtils.toISODate(dt) || '';
        return resource.dates[dateKey] || [];
    }

    getButtonClass(resourceDate: { availablepercentage: number, preferredtimeavl: number }) {
        let classes = {
            'red-button': resourceDate.availablepercentage <= 10,
            'medium-blue-button': this.objLoad.fromTime && this.objLoad.toTime && resourceDate.preferredtimeavl > 0 && resourceDate.availablepercentage > 10,
            'green-button': resourceDate.availablepercentage >= 10 && resourceDate.availablepercentage <= 100 && !(this.objLoad.fromTime && this.objLoad.toTime && resourceDate.preferredtimeavl > 0),
            'lite-red-button': resourceDate.preferredtimeavl === 0 && resourceDate.availablepercentage > 10,
        };
        return classes;
    }
}
