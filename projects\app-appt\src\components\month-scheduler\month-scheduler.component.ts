import { Component, Input, Output, OnInit, EventEmitter, SimpleChanges, ChangeDetectionStrategy } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { CommonModule, DatePipe } from '@angular/common';
import { SlotSelectorComponent, FindlistlegendComponent } from '@app-appt/components';
import { FillHeightDirective } from 'ec-ngcore/ui';
import { dateUtils, ApplicationService, EcSysTextService } from 'ec-ngcore';

import { MaterialPackage } from '@app-appt/utils';
import { DateTime } from 'luxon';
import { MonthViewComponent } from '../month-view/month-view.component';
import { WeekViewComponent } from '../week-view/week-view.component';

interface weekDates {
    days: { date: Date }[];
}

interface WeekDay {
    date: Date;
    dayName: string;
    dayNumber: string;
}
@Component({
    selector: 'his-monthscheduler',
    standalone: true,
    imports: [
        CommonModule,
        FillHeightDirective,
        FindlistlegendComponent,
        MaterialPackage,
        MonthViewComponent,
        WeekViewComponent
    ],
    templateUrl: './month-scheduler.component.html',
    styleUrls: ['./month-scheduler.component.scss'],
    providers: [DatePipe],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class MonthSchedulercomponent implements OnInit {
    @Input() headerdata: any[] = [];
    @Input() objsession?: any[];
    @Input() objectsearch: any;
    @Input() dateStatus: any;
    @Output() arrival = new EventEmitter();
    currentMonth: Date = dateUtils.truncTime(dateUtils.startOfMonth());
    weeks: any[] = [];
    currentDate = dateUtils.truncTime(new Date());
    selectedStartDate: Date;
    selectedEndDate: Date
    minAllowedDate?: Date;
    maxAllowedDate?: Date;
    isArrowDisabled: boolean = true;
    objLoad: any = {};
    slotbooking: any;
    headervalues: any;
    currentView: 'day' | 'week' | 'month' = 'month';
    currentWeekStart = dateUtils.startOfWeek();
    currentDay = new Date();

    currentWeek: weekDates = { days: [] };
    monthWeeks: any[] = [];
    weekDays: WeekDay[] = [];

    visibleRange = 7;
    today = new Date();
    minDate = new Date();
    times: string[] = [];
    isOpen: boolean = false;
    fromTime: string = '';
    toTime: string = '';
    displayValue: string = '';

    constructor(
        public dialog: MatDialog,
        public datePipe: DatePipe,
        private _appService: ApplicationService,
        public _textService: EcSysTextService,
    ) {
        this.selectedStartDate = dateUtils.truncTime(new Date())
        this.selectedEndDate = dateUtils.addMonths(this.selectedStartDate, 1);
        this.setDefaultDateRange();
    }

    ngOnInit(): void {
        this.objLoad.fromDate = this.objectsearch.fromDate;
        this.objLoad.toDate = this.objectsearch.toDate;
        this.currentMonth = new Date(this.objLoad.fromDate);
        this.generateMonthWeeks();
        this.generateWeekDays();
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['headerdata']) {
            this.headervalues = changes['headerdata'].currentValue
        }
    }
    trackByResource(index: number, resource: any): string {
        return resource?.resourceid || index;
    }

    trackBySession(index: number, session: any): string {
        return session?.sessionid || index;
    }

    setView(view: 'day' | 'week' | 'month') {
        this.currentView = view;
        if (view === 'month') {
            this.generateMonthWeeks();
        } else if (view === 'week') {
            this.generateWeekDays();
        } else if (view === 'day') {
            this.currentDay = new Date();
        }
    }
    prevPeriod() {
        if (this.currentView === 'month') {
            const prevMonth = dateUtils.addMonths(this.currentMonth || new Date(), -1);
            const startDate = this.objectsearch.fromDate;

            if (prevMonth >= startDate) {
                this.currentMonth = prevMonth;
                this.generateMonthWeeks();
            } else {
                this._appService.alertDialog({
                    title: 'Warning',
                    message: "* Cannot navigate before your start date."
                });
            }
        } else if (this.currentView === 'week') {
            const prevWeekStart = dateUtils.addDays(this.currentWeekStart, -7);
            const startDate = dateUtils.startOfWeek(this.objectsearch.fromDate);

            if (prevWeekStart >= startDate) {
                this.currentWeekStart = prevWeekStart;
                this.generateWeekDays();
            } else {
                this._appService.alertDialog({
                    title: 'Warning',
                    message: "* Cannot navigate before your start date."
                });
            }
        } else if (this.currentView === 'day') {
            const prevDay = dateUtils.addDays(this.currentDay, -1);
            const startDate = this.objectsearch.fromDate;

            if (prevDay >= startDate) {
                this.currentDay = prevDay;
            } else {
                this._appService.alertDialog({
                    title: 'Warning',
                    message: "* Cannot navigate before your start date."
                });
            }
        }
    }

    nextPeriod() {
        if (this.currentView === 'month') {
            const nextMonth = dateUtils.addMonths(this.currentMonth || new Date(), 1);
            const endDate = this.objectsearch.toDate;

            if (nextMonth <= endDate) {
                this.currentMonth = nextMonth;
                this.generateMonthWeeks();
            } else {
                this._appService.alertDialog({
                    title: 'Warning',
                    message: "* Cannot navigate after your end date."
                });
            }
        } else if (this.currentView === 'week') {
            const nextWeekStart = dateUtils.addDays(this.currentWeekStart, 7);
            const endDate = dateUtils.endOfWeek(this.objectsearch.toDate);

            if (nextWeekStart <= endDate) {
                this.currentWeekStart = nextWeekStart;
                this.generateWeekDays();
            } else {
                this._appService.alertDialog({
                    title: 'Warning',
                    message: "* Cannot navigate after your end date."
                });
            }
        } else if (this.currentView === 'day') {
            const nextDay = dateUtils.addDays(this.currentDay, 1);
            const endDate = this.objectsearch.toDate;

            if (nextDay <= endDate) {
                this.currentDay = nextDay;
            } else {
                this._appService.alertDialog({
                    title: 'Warning',
                    message: "* Cannot navigate after your end date."
                });
            }
        }
    }



    processHeaderData() {
        this.headerdata.forEach(item => {
            /*FIX ME Commented during upgrade to Angular 20*/
            //item.dateheader = moment(item.dateheader); // Ensure date is in moment format if needed
            item.description = item.description || 'No Description'; // Fallback text if null
        });
    }

    trackByIndex(index: number): number {
        return index;
    }
    setDefaultDateRange() {
        const today = new Date();
        const endDay = new Date();
        endDay.setDate(today.getDate() + 6);
        this.objLoad.fromDate = today;
        this.objLoad.toDate = endDay;
    }






    isWithinRange(date: Date): boolean {
        const startDate = this.objLoad.fromDate;
        const endDate = this.objLoad.toDate;
        return date >= startDate && date <= endDate;
    }

    getButtonClass(resourceDate: { availablepercentage: number, preferredtimeavl: number }) {
        let classes = {
            'red-button': resourceDate.availablepercentage <= 10,
            'medium-blue-button': this.objLoad.fromTime && this.objLoad.toTime && resourceDate.preferredtimeavl > 0 && resourceDate.availablepercentage > 10,
            'green-button': resourceDate.availablepercentage >= 10 && resourceDate.availablepercentage <= 100 && !(this.objLoad.fromTime && this.objLoad.toTime && resourceDate.preferredtimeavl > 0),
            'lite-red-button': resourceDate.preferredtimeavl === 0 && resourceDate.availablepercentage > 10,
        };
        return classes;
    }

    slotgenerate(slot: any): void {
        this.slotbooking = slot;
        this.slotbooking.resourceid = slot.resourceid;
    }

    slotgeneratepopup(slot: any): void {
        const dialogData = {
            ...slot,
            clinic: this.objectsearch.clinic,
            speciality: this.objectsearch.speciality,
            resourcetype: this.objectsearch.resourcetype,
            fromtime: this.objectsearch.fromtime,
            totime: this.objectsearch.totime
        };
        console.log(dialogData, 'check ram slot')
        const dialogRef = this.dialog.open(SlotSelectorComponent, {
            disableClose: true,
            autoFocus: true,
            width: "90vw",
            data: dialogData
        });
        dialogRef.afterClosed().subscribe(result => {
            if (result) {
                console.log(result, 'check ram result')
                this.arrival.emit("Arrival");
            }
        });
    }

    enableArrow() {
        this.isArrowDisabled = false;
    }

    disableArrow() {
        this.isArrowDisabled = true;
    }
    getFormattedWeekRange() {
        const weekStart = this._textService.formatDate(this.currentWeekStart);
        const weekEnd = this._textService.formatDate(dateUtils.endOfWeek(this.currentWeekStart));
        return `${weekStart} - ${weekEnd}`;
    }

    generateMonthWeeks(): void {
        this.monthWeeks = [];
        let startOfWeek = dateUtils.startOfWeek(dateUtils.startOfMonth(this.currentMonth));
        let endOfMonth = dateUtils.endOfMonth(this.currentMonth);

        while (startOfWeek < endOfMonth) {
            this.monthWeeks.push({
                days: Array.from({ length: this.visibleRange }, (_, index) => {
                    const dt = dateUtils.addDays(startOfWeek, index);
                    return {
                        date: dt,
                        inMonth: dt.getMonth() === this.currentMonth?.getMonth()
                    };
                })
            });
            startOfWeek = dateUtils.addDays(startOfWeek, 7);
        }
    }

    generateWeekDays(): void {
        this.weekDays = [];
        for (let i = 0; i < 7; i++) {
            const date = dateUtils.addDays(this.currentWeekStart, i);
            const luxonDate = DateTime.fromJSDate(date);

            this.weekDays.push({
                date: date,
                dayName: luxonDate.toFormat('ccc'), // Short day name (Mon, Tue, etc.)
                dayNumber: luxonDate.toFormat('dd')  // Day number (01, 02, etc.)
            });
        }
    }

    getDescription(date: Date): string {
        const entry = this.headervalues?.find((d: any) => d.dateheader === date);
        return entry ? entry.description : '';
    }

    getResourceSessions(resource: any, dt: Date): any[] {
        if (!resource || !resource.dates || !dt) return [];
        const dateKey = dateUtils.toISODate(dt) || '';
        return resource.dates[dateKey] || [];
    }
}
