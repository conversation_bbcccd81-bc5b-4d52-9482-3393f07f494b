<div class="table-carousel-container">
    <button [ngClass]="{'pointer-arrow': true, 'disabled': isArrowDisabled}" (mouseenter)="enableArrow()"
        (mouseleave)="disableArrow()" alt="arrow" class="arrow left-arrow" (click)="prevPeriod()">&#10094;
    </button>
    <div class="grow">
        <div class="flex  pb-1">
            <div class="justify-start space-x-2">
                <button (click)="setView('week')"
                    [ngClass]="{'bg-blue-500 text-white': currentView === 'week', 'bg-gray-200': currentView !== 'week'}"
                    class="px-4 py-1 rounded">
                    Week
                </button>
                <button (click)="setView('month')"
                    [ngClass]="{'bg-blue-500 text-white': currentView === 'month', 'bg-gray-200': currentView !== 'month'}"
                    class="px-4 py-1 rounded">
                    Month
                </button>
            </div>
            <div class="justify-end ml-auto mt-auto">
                <his-findlistlegend></his-findlistlegend>
            </div>
        </div>
        <div class="calendar-navigation">
           @if (currentView == 'week'){
              <b>{{currentWeekStart | date:'MMM yyyy'}}</b>
           }
           @else {
              <b>{{currentMonth | date:'MMM yyyy'}}</b>

           }
        </div>

        <div class="calendar" ecui-height-fill>
            @switch(currentView) {
                @case ('month') {
                   <his-month-view
                       [monthWeeks]="monthWeeks"
                       [objsession]="objsession"
                       [headervalues]="headervalues"
                       [objLoad]="objLoad"
                       (slotSelected)="slotgeneratepopup($event)">
                   </his-month-view>
                }
                @default {
                   <his-week-view
                       [weekDays]="weekDays"
                       [objsession]="objsession"
                       [headervalues]="headervalues"
                       [objLoad]="objLoad"
                       (slotSelected)="slotgeneratepopup($event)">
                   </his-week-view>
                }
            }
        </div>
    </div>
    <button [ngClass]="{'pointer-arrow': true, 'disabled': isArrowDisabled}" (mouseenter)="enableArrow()"
        (mouseleave)="disableArrow()" alt="arrow" class="arrow right-arrow" (click)="nextPeriod()">&#10095;
    </button>
</div>

 